// DigaBusiness.com 网站规则配置
// 网站: https://www.digabusiness.com/submit.php
// 最后更新: 2025-07-26

export const SITE_RULE = {
  // 基本信息
  domain: 'www.digabusiness.com',
  siteName: 'Diga Business Directory',
  priority: 1,
  lastUpdated: '2025-07-26',

  // 字段映射规则
  fieldMappings: {
    // 网站标题 -> Title
    siteName: {
      selectors: [
        'input[name="TITLE"]',
        'input[maxlength="100"]',
        'input[size="45"][maxlength="100"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，最多100字符，建议3-5个词'
    },

    // 网站URL -> Website
    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'input[maxlength="255"]',
        'input[size="45"][maxlength="255"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址，最多255字符'
    },

    // 网站描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        'textarea[rows="3"][cols="39"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 1000,
      notes: '网站描述，最多1000字符，有实时计数器'
    },

    // 服务/产品 -> Services/Products Offered
    features: {
      selectors: [
        'input[name="OFFER"]',
        'input[size="40"][maxlength="255"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '提供的服务或产品，最多255字符'
    },

    // Twitter链接 -> Twitter Follow Link
    twitterUrl: {
      selectors: [
        'input[name="TWITTER_URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Twitter关注链接'
    },

    // Facebook链接 -> Connect on Facebook Link
    facebookUrl: {
      selectors: [
        'input[name="FACEBOOK_URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Facebook连接链接'
    },

    // LinkedIn链接 -> LinkedIn URL
    linkedinUrl: {
      selectors: [
        'input[name="LINKEDIN_URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'LinkedIn个人资料链接'
    },

    // 提交者姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="OWNER_NAME"]',
        'input[maxlength="50"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，最多50字符'
    },

    // 联系邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="OWNER_EMAIL"]',
        'input[size="45"][maxlength="255"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址'
    },

    // 分类 -> Category
    category: {
      selectors: [
        'input[name="CATEGORY_ID"]',
        '#CATEGORY_ID'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站分类，通过分类树选择'
    },

    // 地址信息 -> Address
    streetAddress: {
      selectors: [
        'input[name="ADDRESS"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '物理地址，用于Google地图显示'
    },

    // 城市 -> City
    city: {
      selectors: [
        'input[name="CITY"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '城市或镇，用于地图显示'
    },

    // 州/省 -> State/Province
    state: {
      selectors: [
        'input[name="STATE"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '州、省或地区'
    },

    // 邮编 -> Zip/Postal Code
    zipCode: {
      selectors: [
        'input[name="ZIP"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '邮政编码'
    },

    // 电话号码 -> Phone Number
    phone: {
      selectors: [
        'input[name="PHONE_NUMBER"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '联系电话'
    },

    // 验证码 -> Enter the code shown
    captcha: {
      selectors: [
        'input[name="CAPTCHA"]',
        '#CAPTCHA'
      ],
      method: 'value',
      validation: 'required',
      notes: '验证码，5位字符'
    },

    // 链接类型 -> Link Type
    linkType: {
      selectors: [
        'input[name="LINK_TYPE"]'
      ],
      method: 'radio',
      validation: 'required',
      options: ['featured', 'topregular', 'normal'],
      defaultValue: 'normal',
      notes: '链接类型：特色链接($59)、顶级普通链接($19)、普通链接(免费)'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'input[name="submit"], input[value="Continue"]',
    submitMethod: 'manual',
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleDigaBusinessSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'fullName', 'contactEmail', 'category', 'captcha', 'linkType'],
      optionalFields: ['siteDescription', 'features', 'twitterUrl', 'facebookUrl', 'linkedinUrl', 'streetAddress', 'city', 'state', 'zipCode', 'phone'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'Diga Business Directory 商业目录',
      '提供三种链接类型：',
      '- 特色链接：$59终身，48小时审核，显著位置，包含深度链接',
      '- 顶级普通链接：$19终身，限量显示在普通链接之上',
      '- 普通链接：免费，适合小企业推广',
      '支持深度链接功能（额外$5/链接）',
      '包含验证码验证',
      '支持社交媒体链接（Twitter、Facebook、LinkedIn）',
      '支持地理位置信息，用于Google地图显示',
      '支持IM联系方式（AIM、Yahoo、MSN、GTalk）',
      '描述限制1000字符，有实时计数器',
      '标题建议3-5个词，不要过于通用或过于详细',
      '分类通过分类树选择',
      '传统表格样式界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleDigaBusinessSubmission(data, rule) {
  console.log('Processing Diga Business Directory form submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置默认链接类型为普通链接（免费）
  if (!processedData.linkType) {
    processedData.linkType = 'normal';
  }
  
  // 处理描述长度限制
  if (processedData.siteDescription && processedData.siteDescription.length > 1000) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 997) + '...';
  }
  
  return processedData;
}
