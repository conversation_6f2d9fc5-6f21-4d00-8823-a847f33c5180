// DigaBusiness.com 网站规则配置
// 网站: https://www.digabusiness.com/submit.php
// 最后更新: 2025-07-26

export const SITE_RULE = {
  // 基本信息
  domain: 'www.digabusiness.com',
  siteName: 'Diga Business Directory',
  priority: 1,
  lastUpdated: '2025-07-26',

  // 字段映射规则
  fieldMappings: {
    // 网站标题 -> Title
    siteName: {
      selectors: [
        'input[name="TITLE"]',
        'input[maxlength="100"]',
        'input[size="45"][maxlength="100"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，最多100字符，建议3-5个词'
    },

    // 网站URL -> Website
    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'input[maxlength="255"]',
        'input[size="45"][maxlength="255"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址，最多255字符'
    },

    // 网站描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        'textarea[rows="3"][cols="39"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 1000,
      notes: '网站描述，最多1000字符，有实时计数器'
    },

    // 链接类型 -> Link Type
    linkType: {
      selectors: [
        'input[name="LINK_TYPE"]'
      ],
      method: 'radio',
      validation: 'required',
      options: ['featured', 'topregular', 'normal'],
      defaultValue: 'normal',
      notes: '链接类型：特色链接($59)、顶级普通链接($19)、普通链接(免费)'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'input[type="submit"], button[type="submit"]',
    submitMethod: 'manual',
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleDigaBusinessSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'linkType'],
      optionalFields: ['siteDescription'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'Diga Business Directory 商业目录',
      '提供三种链接类型：',
      '- 特色链接：$59终身，48小时审核，显著位置，包含深度链接',
      '- 顶级普通链接：$19终身，限量显示在普通链接之上',
      '- 普通链接：免费，适合小企业推广',
      '支持深度链接功能（额外$5/链接）',
      '描述限制1000字符，有实时计数器',
      '标题建议3-5个词，不要过于通用或过于详细',
      '传统表格样式界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleDigaBusinessSubmission(data, rule) {
  console.log('Processing Diga Business Directory form submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置默认链接类型为普通链接（免费）
  if (!processedData.linkType) {
    processedData.linkType = 'normal';
  }
  
  // 处理描述长度限制
  if (processedData.siteDescription && processedData.siteDescription.length > 1000) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 997) + '...';
  }
  
  return processedData;
}
