// AllBusinessDirectory.biz 网站规则配置
// 网站: https://www.allbusinessdirectory.biz/link_submit.php
// 最后更新: 2025-07-26

export const SITE_RULE = {
  // 基本信息
  domain: 'www.allbusinessdirectory.biz',
  siteName: 'All Business Directory',
  priority: 1,
  lastUpdated: '2025-07-26',

  // 字段映射规则
  fieldMappings: {
    // 姓名 -> Name
    fullName: {
      selectors: [
        'input[name="name"]',
        'input[maxlength="100"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 100,
      notes: '提交者姓名，最多100字符'
    },

    // 邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[maxlength="255"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，某些邮箱地址可能被禁用'
    },

    // 网站URL -> URL
    siteUrl: {
      selectors: [
        'input[name="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 互惠链接URL -> Reciprocal URL
    reciprocalUrl: {
      selectors: [
        'input[name="recip"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '互惠链接URL，可选'
    },

    // 网站标题 -> Title
    siteName: {
      selectors: [
        'input[name="title"]',
        'input[maxlength="55"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 55,
      notes: '网站标题，最多55字符'
    },

    // 网站描述 -> Description
    siteDescription: {
      selectors: [
        'input[name="description"]'
      ],
      method: 'value',
      validation: 'required',
      minLength: 149,
      maxLength: 255,
      notes: '网站描述，最少149字符，最多255字符'
    },

    // 详细信息 -> Further Company/Product/Service Information
    detailedIntro: {
      selectors: [
        'textarea[name="detail"]',
        'textarea[rows="10"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 1500,
      notes: '公司/产品/服务详细信息，最多1500字符'
    },

    // 分类 -> Select Category
    category: {
      selectors: [
        'select[name="categories[]"]'
      ],
      method: 'select',
      validation: 'required',
      notes: '选择分类，必填'
    },

    // 关键词 -> Keywords
    keywords: {
      selectors: [
        'input[name="user_item_1"]',
        'input[maxlength="275"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 275,
      notes: '关键词，逗号空格分隔，最多275字符'
    },

    // 建议新分类 -> Suggest New Category
    suggestCategory: {
      selectors: [
        'input[name="user_item_2"]',
        'input[maxlength="50"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 50,
      notes: '建议新分类，可选'
    },

    // 评论/指示 -> Comments/Instructions To Us
    comments: {
      selectors: [
        'input[name="user_item_3"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '给我们的评论或指示，可选'
    },

    // 发现来源 -> Where did you find us?
    referralSource: {
      selectors: [
        'input[name="user_item_4"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '您是如何找到我们的，必填'
    },

    // Follow链接选项 -> Uncheck for "nofollow" link. Check for "follow" link
    followLink: {
      selectors: [
        'input[name="user_item_31"]',
        'input[type="checkbox"][value="1"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      defaultValue: true,
      notes: '勾选获得follow链接，取消勾选为nofollow链接'
    },

    // 密码 -> Password
    password: {
      selectors: [
        'input[name="password"]',
        'input[maxlength="12"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 12,
      notes: '密码，12字符'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'input[name="B1"], input[value="Add URL"]',
    submitMethod: 'manual',
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleAllBusinessDirectorySubmission',
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteUrl', 'siteName', 'siteDescription', 'category', 'keywords', 'referralSource', 'password'],
      optionalFields: ['reciprocalUrl', 'detailedIntro', 'suggestCategory', 'comments', 'followLink'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        siteName: 55,
        siteDescription: { min: 149, max: 255 },
        detailedIntro: 1500,
        keywords: 275,
        suggestCategory: 50,
        password: 12
      }
    },
    notes: [
      'All Business Directory 商业目录',
      '传统的网站目录服务',
      '严格的字符限制要求：',
      '- 标题：最多55字符',
      '- 描述：最少149字符，最多255字符',
      '- 详细信息：最多1500字符',
      '- 关键词：最多275字符，逗号空格分隔',
      '- 密码：12字符',
      '某些邮箱地址可能被禁用',
      '支持互惠链接',
      '支持follow/nofollow链接选择',
      '需要提供发现来源',
      '传统表格样式界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleAllBusinessDirectorySubmission(data, rule) {
  console.log('Processing All Business Directory form submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 处理互惠链接URL格式
  if (processedData.reciprocalUrl && processedData.reciprocalUrl && !processedData.reciprocalUrl.startsWith('http')) {
    processedData.reciprocalUrl = 'https://' + processedData.reciprocalUrl;
  }
  
  // 确保描述符合最少149字符要求
  if (processedData.siteDescription && processedData.siteDescription.length < 149) {
    // 如果描述太短，可以从详细信息中补充
    if (processedData.detailedIntro) {
      const combined = processedData.siteDescription + ' ' + processedData.detailedIntro;
      processedData.siteDescription = combined.substring(0, 255);
    }
  }
  
  // 处理标题长度限制
  if (processedData.siteName && processedData.siteName.length > 55) {
    processedData.siteName = processedData.siteName.substring(0, 55);
  }
  
  // 设置默认follow链接为true
  if (processedData.followLink === undefined) {
    processedData.followLink = true;
  }
  
  // 生成12位密码（如果没有提供）
  if (!processedData.password) {
    processedData.password = Math.random().toString(36).substring(2, 14);
  }
  
  // 设置默认发现来源
  if (!processedData.referralSource) {
    processedData.referralSource = 'Search Engine';
  }
  
  return processedData;
}
